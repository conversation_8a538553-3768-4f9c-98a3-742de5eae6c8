# ARCHITECTURE TECHNIQUE DÉTAILLÉE
## Application de Recouvrement de Créances

### 1. STRUCTURE DU PROJET

```
RecouvrementCreances.Solution/
├── RecouvrementCreances.Entities/          # Couche Entités
│   ├── Models/
│   │   ├── Client.cs
│   │   ├── Facture.cs
│   │   ├── Paiement.cs
│   │   ├── Echeance.cs
│   │   ├── Litige.cs
│   │   ├── PlanPaiement.cs
│   │   ├── Relance.cs
│   │   ├── Document.cs
│   │   ├── Contact.cs
│   │   ├── Utilisateur.cs
│   │   └── AuditLog.cs
│   ├── Enums/
│   │   ├── StatutFacture.cs
│   │   ├── TypePaiement.cs
│   │   ├── NiveauRelance.cs
│   │   └── TypeDocument.cs
│   └── DTOs/
│       ├── ClientDto.cs
│       ├── FactureDto.cs
│       └── DashboardDto.cs
├── RecouvrementCreances.Data/               # Couche Accès Données
│   ├── Interfaces/
│   │   ├── IClientRepository.cs
│   │   ├── IFactureRepository.cs
│   │   ├── IPaiementRepository.cs
│   │   └── IUnitOfWork.cs
│   ├── Repositories/
│   │   ├── BaseRepository.cs
│   │   ├── ClientRepository.cs
│   │   ├── FactureRepository.cs
│   │   ├── PaiementRepository.cs
│   │   └── UnitOfWork.cs
│   ├── Context/
│   │   └── DatabaseContext.cs
│   └── Scripts/
│       ├── CreateDatabase.sql
│       ├── CreateTables.sql
│       └── SeedData.sql
├── RecouvrementCreances.Business/           # Couche Métier
│   ├── Interfaces/
│   │   ├── IClientService.cs
│   │   ├── IFactureService.cs
│   │   ├── IPaiementService.cs
│   │   ├── IRelanceService.cs
│   │   └── IReportService.cs
│   ├── Services/
│   │   ├── ClientService.cs
│   │   ├── FactureService.cs
│   │   ├── PaiementService.cs
│   │   ├── RelanceService.cs
│   │   ├── ReportService.cs
│   │   └── SecurityService.cs
│   ├── Validators/
│   │   ├── ClientValidator.cs
│   │   ├── FactureValidator.cs
│   │   └── PaiementValidator.cs
│   └── Helpers/
│       ├── CalculHelper.cs
│       ├── EmailHelper.cs
│       └── DocumentHelper.cs
├── RecouvrementCreances.UI/                 # Interface Utilisateur
│   ├── Forms/
│   │   ├── MainForm.cs + .designer.cs
│   │   ├── LoginForm.cs + .designer.cs
│   │   ├── ClientForm.cs + .designer.cs
│   │   ├── FactureForm.cs + .designer.cs
│   │   ├── PaiementForm.cs + .designer.cs
│   │   ├── RelanceForm.cs + .designer.cs
│   │   └── ReportForm.cs + .designer.cs
│   ├── UserControls/
│   │   ├── ClientListControl.cs + .designer.cs
│   │   ├── FactureListControl.cs + .designer.cs
│   │   ├── DashboardControl.cs + .designer.cs
│   │   └── CalendarControl.cs + .designer.cs
│   ├── Helpers/
│   │   ├── FormHelper.cs
│   │   ├── GridHelper.cs
│   │   └── ValidationHelper.cs
│   └── Resources/
│       ├── Icons/
│       ├── Images/
│       └── Strings.resx
└── RecouvrementCreances.Tests/              # Tests Unitaires
    ├── Business/
    ├── Data/
    └── UI/
```

### 2. MODÈLE DE DONNÉES DÉTAILLÉ

#### 2.1 Entités Principales

**Client**
```csharp
public class Client
{
    public int ClientId { get; set; }
    public string CodeClient { get; set; }
    public string RaisonSociale { get; set; }
    public string Siret { get; set; }
    public string Adresse { get; set; }
    public string CodePostal { get; set; }
    public string Ville { get; set; }
    public string Pays { get; set; }
    public string Telephone { get; set; }
    public string Email { get; set; }
    public decimal LimiteCredit { get; set; }
    public int ScoreRisque { get; set; }
    public DateTime DateCreation { get; set; }
    public bool Actif { get; set; }
    
    // Relations
    public virtual ICollection<Facture> Factures { get; set; }
    public virtual ICollection<Contact> Contacts { get; set; }
}
```

**Facture**
```csharp
public class Facture
{
    public int FactureId { get; set; }
    public string NumeroFacture { get; set; }
    public int ClientId { get; set; }
    public DateTime DateFacture { get; set; }
    public DateTime DateEcheance { get; set; }
    public decimal MontantHT { get; set; }
    public decimal MontantTTC { get; set; }
    public decimal TauxTVA { get; set; }
    public StatutFacture Statut { get; set; }
    public string Description { get; set; }
    public DateTime DateCreation { get; set; }
    public int UtilisateurCreation { get; set; }
    
    // Relations
    public virtual Client Client { get; set; }
    public virtual ICollection<Paiement> Paiements { get; set; }
    public virtual ICollection<Echeance> Echeances { get; set; }
    public virtual ICollection<Litige> Litiges { get; set; }
}
```

#### 2.2 Base de Données SQL Server

**Script de Création des Tables Principales**
```sql
-- Table Clients
CREATE TABLE Clients (
    ClientId INT IDENTITY(1,1) PRIMARY KEY,
    CodeClient NVARCHAR(20) NOT NULL UNIQUE,
    RaisonSociale NVARCHAR(255) NOT NULL,
    Siret NVARCHAR(14),
    Adresse NVARCHAR(500),
    CodePostal NVARCHAR(10),
    Ville NVARCHAR(100),
    Pays NVARCHAR(100) DEFAULT 'France',
    Telephone NVARCHAR(20),
    Email NVARCHAR(255),
    LimiteCredit DECIMAL(18,2) DEFAULT 0,
    ScoreRisque INT DEFAULT 0,
    DateCreation DATETIME2 DEFAULT GETDATE(),
    Actif BIT DEFAULT 1,
    
    INDEX IX_Clients_CodeClient (CodeClient),
    INDEX IX_Clients_RaisonSociale (RaisonSociale)
);

-- Table Factures
CREATE TABLE Factures (
    FactureId INT IDENTITY(1,1) PRIMARY KEY,
    NumeroFacture NVARCHAR(50) NOT NULL UNIQUE,
    ClientId INT NOT NULL,
    DateFacture DATE NOT NULL,
    DateEcheance DATE NOT NULL,
    MontantHT DECIMAL(18,2) NOT NULL,
    MontantTTC DECIMAL(18,2) NOT NULL,
    TauxTVA DECIMAL(5,2) NOT NULL,
    Statut INT NOT NULL DEFAULT 0,
    Description NVARCHAR(1000),
    DateCreation DATETIME2 DEFAULT GETDATE(),
    UtilisateurCreation INT NOT NULL,
    
    FOREIGN KEY (ClientId) REFERENCES Clients(ClientId),
    INDEX IX_Factures_ClientId (ClientId),
    INDEX IX_Factures_DateEcheance (DateEcheance),
    INDEX IX_Factures_Statut (Statut)
);

-- Table Paiements
CREATE TABLE Paiements (
    PaiementId INT IDENTITY(1,1) PRIMARY KEY,
    FactureId INT NOT NULL,
    MontantPaiement DECIMAL(18,2) NOT NULL,
    DatePaiement DATE NOT NULL,
    TypePaiement INT NOT NULL,
    NumeroCheque NVARCHAR(50),
    Banque NVARCHAR(100),
    Commentaire NVARCHAR(500),
    DateCreation DATETIME2 DEFAULT GETDATE(),
    UtilisateurCreation INT NOT NULL,
    
    FOREIGN KEY (FactureId) REFERENCES Factures(FactureId),
    INDEX IX_Paiements_FactureId (FactureId),
    INDEX IX_Paiements_DatePaiement (DatePaiement)
);
```

### 3. COUCHE BUSINESS - SERVICES MÉTIER

#### 3.1 Service de Gestion des Factures
```csharp
public interface IFactureService
{
    Task<IEnumerable<FactureDto>> GetFacturesAsync(int? clientId = null);
    Task<FactureDto> GetFactureByIdAsync(int factureId);
    Task<int> CreateFactureAsync(FactureDto facture);
    Task UpdateFactureAsync(FactureDto facture);
    Task DeleteFactureAsync(int factureId);
    Task<decimal> CalculerSoldeFactureAsync(int factureId);
    Task<IEnumerable<FactureDto>> GetFacturesEchuesAsync();
    Task<bool> ValiderFactureAsync(FactureDto facture);
}
```

#### 3.2 Service de Relances Automatiques
```csharp
public interface IRelanceService
{
    Task ExecuterRelancesAutomatiquesAsync();
    Task<IEnumerable<RelanceDto>> GetRelancesClientAsync(int clientId);
    Task CreerRelanceAsync(int factureId, TypeRelance type);
    Task<bool> EnvoyerEmailRelanceAsync(int relanceId);
    Task ProgrammerRelanceSuivanteAsync(int factureId);
}
```

### 4. COUCHE DATA - ACCÈS AUX DONNÉES

#### 4.1 Repository Pattern avec Dapper
```csharp
public class FactureRepository : BaseRepository<Facture>, IFactureRepository
{
    public async Task<IEnumerable<Facture>> GetFacturesEchuesAsync()
    {
        const string sql = @"
            SELECT f.*, c.RaisonSociale, c.Email
            FROM Factures f
            INNER JOIN Clients c ON f.ClientId = c.ClientId
            WHERE f.DateEcheance < GETDATE() 
            AND f.Statut = @StatutEnCours
            ORDER BY f.DateEcheance";
            
        return await Connection.QueryAsync<Facture>(sql, 
            new { StatutEnCours = (int)StatutFacture.EnCours });
    }
    
    public async Task<decimal> CalculerSoldeFactureAsync(int factureId)
    {
        const string sql = @"
            SELECT f.MontantTTC - ISNULL(SUM(p.MontantPaiement), 0) as Solde
            FROM Factures f
            LEFT JOIN Paiements p ON f.FactureId = p.FactureId
            WHERE f.FactureId = @FactureId
            GROUP BY f.MontantTTC";
            
        return await Connection.QuerySingleOrDefaultAsync<decimal>(sql, 
            new { FactureId = factureId });
    }
}
```

### 5. INTERFACE UTILISATEUR - WINDOWS FORMS

#### 5.1 Formulaire Principal avec Designer
```csharp
// MainForm.cs
public partial class MainForm : Form
{
    private readonly IClientService _clientService;
    private readonly IFactureService _factureService;
    private readonly IDashboardService _dashboardService;
    
    public MainForm(IClientService clientService, 
                   IFactureService factureService,
                   IDashboardService dashboardService)
    {
        InitializeComponent();
        _clientService = clientService;
        _factureService = factureService;
        _dashboardService = dashboardService;
        
        InitializeAsync();
    }
    
    private async void InitializeAsync()
    {
        await LoadDashboardAsync();
        SetupEventHandlers();
        ConfigureGrids();
    }
    
    private async Task LoadDashboardAsync()
    {
        var dashboard = await _dashboardService.GetDashboardDataAsync();
        UpdateDashboardControls(dashboard);
    }
}

// MainForm.Designer.cs
partial class MainForm
{
    private System.ComponentModel.IContainer components = null;
    private MenuStrip menuStrip1;
    private ToolStrip toolStrip1;
    private StatusStrip statusStrip1;
    private SplitContainer splitContainer1;
    private DataGridView dgvFactures;
    private Panel pnlDashboard;
    
    protected override void Dispose(bool disposing)
    {
        if (disposing && (components != null))
        {
            components.Dispose();
        }
        base.Dispose(disposing);
    }
    
    private void InitializeComponent()
    {
        // Code généré par le Designer Visual Studio
        // Configuration complète des contrôles
    }
}
```

### 6. GESTION DE LA CONCURRENCE

#### 6.1 Verrouillage Optimiste
```csharp
public class BaseEntity
{
    public byte[] RowVersion { get; set; }
    public DateTime DateModification { get; set; }
    public int UtilisateurModification { get; set; }
}

public async Task UpdateFactureAsync(FactureDto facture)
{
    const string sql = @"
        UPDATE Factures 
        SET MontantTTC = @MontantTTC,
            DateModification = GETDATE(),
            UtilisateurModification = @UserId
        WHERE FactureId = @FactureId 
        AND RowVersion = @RowVersion";
        
    var rowsAffected = await Connection.ExecuteAsync(sql, facture);
    
    if (rowsAffected == 0)
    {
        throw new ConcurrencyException(
            "La facture a été modifiée par un autre utilisateur.");
    }
}
```

### 7. SÉCURITÉ ET AUDIT

#### 7.1 Journal d'Audit
```csharp
public class AuditService : IAuditService
{
    public async Task LogActionAsync(string action, string tableName, 
                                   int recordId, int userId, 
                                   object oldValues = null, 
                                   object newValues = null)
    {
        var auditLog = new AuditLog
        {
            Action = action,
            TableName = tableName,
            RecordId = recordId,
            UserId = userId,
            OldValues = JsonConvert.SerializeObject(oldValues),
            NewValues = JsonConvert.SerializeObject(newValues),
            Timestamp = DateTime.Now,
            IPAddress = GetClientIPAddress()
        };
        
        await _auditRepository.CreateAsync(auditLog);
    }
}
```

### 8. CONFIGURATION ET DÉPLOIEMENT

#### 8.1 Configuration App.config
```xml
<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <connectionStrings>
    <add name="DefaultConnection" 
         connectionString="Server=.\SQLEXPRESS;Database=RecouvrementCreances;Integrated Security=true;TrustServerCertificate=true;" />
  </connectionStrings>
  
  <appSettings>
    <add key="EmailServer" value="smtp.company.com" />
    <add key="EmailPort" value="587" />
    <add key="DocumentPath" value="C:\RecouvrementDocs\" />
    <add key="MaxConcurrentUsers" value="20" />
  </appSettings>
</configuration>
```

### 9. TESTS ET QUALITÉ

#### 9.1 Tests Unitaires
```csharp
[TestClass]
public class FactureServiceTests
{
    private Mock<IFactureRepository> _mockRepository;
    private FactureService _service;
    
    [TestInitialize]
    public void Setup()
    {
        _mockRepository = new Mock<IFactureRepository>();
        _service = new FactureService(_mockRepository.Object);
    }
    
    [TestMethod]
    public async Task CalculerSolde_FactureAvecPaiements_RetourneSoldeCorrect()
    {
        // Arrange
        var factureId = 1;
        var montantFacture = 1000m;
        var montantPaiements = 300m;
        var soldeAttendu = 700m;
        
        _mockRepository.Setup(r => r.CalculerSoldeFactureAsync(factureId))
                      .ReturnsAsync(soldeAttendu);
        
        // Act
        var resultat = await _service.CalculerSoldeFactureAsync(factureId);
        
        // Assert
        Assert.AreEqual(soldeAttendu, resultat);
    }
}
```

---

**Cette architecture technique détaillée fournit une base solide pour le développement de l'application de recouvrement de créances, en respectant les bonnes pratiques du développement .NET et les exigences de performance pour traiter plus de 100 factures par jour.**
