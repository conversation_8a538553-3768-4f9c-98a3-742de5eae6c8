# SPÉCIFICATIONS DES FORMULAIRES WINDOWS FORMS
## Application de Recouvrement de Créances

### 1. ARCHITECTURE DES FORMULAIRES

#### 1.1 Structure des Fichiers
Chaque formulaire doit avoir obligatoirement :
- **FormName.cs** : Code-behind avec la logique métier
- **FormName.Designer.cs** : Code généré par le Designer Visual Studio
- **FormName.resx** : Ressources du formulaire

### 2. FORMULAIRE PRINCIPAL (MainForm)

#### 2.1 Spécifications Générales
- **Taille** : 1400x900 pixels (redimensionnable)
- **Position** : Centré à l'écran
- **État** : Maximisé par défaut
- **Icône** : Logo de l'application

#### 2.2 Structure du MainForm.Designer.cs
```csharp
partial class MainForm
{
    private System.ComponentModel.IContainer components = null;
    
    // Menu principal
    private MenuStrip menuStrip1;
    private ToolStripMenuItem fichierToolStripMenuItem;
    private ToolStripMenuItem clientsToolStripMenuItem;
    private ToolStripMenuItem facturesToolStripMenuItem;
    private ToolStripMenuItem paiementsToolStripMenuItem;
    private ToolStripMenuItem relancesToolStripMenuItem;
    private ToolStripMenuItem rapportsToolStripMenuItem;
    private ToolStripMenuItem administrationToolStripMenuItem;
    
    // Barre d'outils
    private ToolStrip toolStrip1;
    private ToolStripButton tsbNouveauClient;
    private ToolStripButton tsbNouvelleFacture;
    private ToolStripButton tsbNouveauPaiement;
    private ToolStripSeparator toolStripSeparator1;
    private ToolStripButton tsbRechercher;
    private ToolStripButton tsbActualiser;
    
    // Barre de statut
    private StatusStrip statusStrip1;
    private ToolStripStatusLabel tslUtilisateur;
    private ToolStripStatusLabel tslDate;
    private ToolStripStatusLabel tslNombreEnregistrements;
    
    // Conteneur principal
    private SplitContainer splitContainerMain;
    private Panel pnlGauche;
    private Panel pnlDroite;
    
    // Tableau de bord (panneau gauche)
    private GroupBox gbTableauBord;
    private Label lblTotalCreances;
    private Label lblCreancesEchues;
    private Label lblTauxRecouvrement;
    private Chart chartEvolutionCreances;
    
    // Zone de travail (panneau droit)
    private TabControl tabControlPrincipal;
    private TabPage tpFactures;
    private TabPage tpClients;
    private TabPage tpPaiements;
    private TabPage tpRelances;
    private TabPage tpRapports;
    
    // DataGridView pour les factures
    private DataGridView dgvFactures;
    private DataGridViewTextBoxColumn colNumeroFacture;
    private DataGridViewTextBoxColumn colClient;
    private DataGridViewTextBoxColumn colDateFacture;
    private DataGridViewTextBoxColumn colDateEcheance;
    private DataGridViewTextBoxColumn colMontantTTC;
    private DataGridViewTextBoxColumn colSolde;
    private DataGridViewTextBoxColumn colStatut;
    private DataGridViewButtonColumn colActions;
    
    // Panneau de filtres
    private Panel pnlFiltres;
    private Label lblFiltreClient;
    private ComboBox cmbFiltreClient;
    private Label lblFiltreStatut;
    private ComboBox cmbFiltreStatut;
    private Label lblFiltrePeriode;
    private DateTimePicker dtpDebut;
    private DateTimePicker dtpFin;
    private Button btnAppliquerFiltres;
    private Button btnReinitialiserFiltres;
}
```

### 3. FORMULAIRE CLIENT (ClientForm)

#### 3.1 Spécifications
- **Type** : Formulaire modal
- **Taille** : 800x600 pixels
- **Modes** : Création, Modification, Consultation

#### 3.2 Structure du ClientForm.Designer.cs
```csharp
partial class ClientForm
{
    private System.ComponentModel.IContainer components = null;
    
    // Informations générales
    private GroupBox gbInformationsGenerales;
    private Label lblCodeClient;
    private TextBox txtCodeClient;
    private Label lblRaisonSociale;
    private TextBox txtRaisonSociale;
    private Label lblSiret;
    private TextBox txtSiret;
    
    // Adresse
    private GroupBox gbAdresse;
    private Label lblAdresse;
    private TextBox txtAdresse;
    private Label lblCodePostal;
    private TextBox txtCodePostal;
    private Label lblVille;
    private TextBox txtVille;
    private Label lblPays;
    private ComboBox cmbPays;
    
    // Contact
    private GroupBox gbContact;
    private Label lblTelephone;
    private TextBox txtTelephone;
    private Label lblEmail;
    private TextBox txtEmail;
    private Label lblSiteWeb;
    private TextBox txtSiteWeb;
    
    // Paramètres financiers
    private GroupBox gbParametresFinanciers;
    private Label lblLimiteCredit;
    private NumericUpDown nudLimiteCredit;
    private Label lblDelaiPaiement;
    private NumericUpDown nudDelaiPaiement;
    private Label lblTauxInteret;
    private NumericUpDown nudTauxInteret;
    
    // Évaluation des risques
    private GroupBox gbEvaluationRisques;
    private Label lblScoreRisque;
    private ProgressBar pbScoreRisque;
    private Label lblNiveauRisque;
    private ComboBox cmbNiveauRisque;
    private CheckBox chkClientBloque;
    
    // Onglets pour informations détaillées
    private TabControl tabControlClient;
    private TabPage tpFactures;
    private TabPage tpPaiements;
    private TabPage tpContacts;
    private TabPage tpDocuments;
    private TabPage tpHistorique;
    
    // Boutons d'action
    private Panel pnlBoutons;
    private Button btnEnregistrer;
    private Button btnAnnuler;
    private Button btnSupprimer;
    private Button btnImprimer;
}
```

### 4. FORMULAIRE FACTURE (FactureForm)

#### 4.1 Spécifications
- **Type** : Formulaire modal
- **Taille** : 900x700 pixels
- **Validation** : Temps réel avec indicateurs visuels

#### 4.2 Structure du FactureForm.Designer.cs
```csharp
partial class FactureForm
{
    private System.ComponentModel.IContainer components = null;
    
    // En-tête de facture
    private GroupBox gbEnTeteFacture;
    private Label lblNumeroFacture;
    private TextBox txtNumeroFacture;
    private Label lblDateFacture;
    private DateTimePicker dtpDateFacture;
    private Label lblDateEcheance;
    private DateTimePicker dtpDateEcheance;
    
    // Informations client
    private GroupBox gbClient;
    private Label lblClient;
    private ComboBox cmbClient;
    private Button btnNouveauClient;
    private Label lblAdresseFacturation;
    private TextBox txtAdresseFacturation;
    
    // Lignes de facture
    private GroupBox gbLignesFacture;
    private DataGridView dgvLignesFacture;
    private DataGridViewTextBoxColumn colDescription;
    private DataGridViewTextBoxColumn colQuantite;
    private DataGridViewTextBoxColumn colPrixUnitaire;
    private DataGridViewTextBoxColumn colTauxTVA;
    private DataGridViewTextBoxColumn colMontantHT;
    private DataGridViewTextBoxColumn colMontantTTC;
    private DataGridViewButtonColumn colSupprimer;
    
    // Boutons pour lignes
    private Panel pnlBoutonsLignes;
    private Button btnAjouterLigne;
    private Button btnSupprimerLigne;
    
    // Totaux
    private GroupBox gbTotaux;
    private Label lblTotalHT;
    private TextBox txtTotalHT;
    private Label lblTotalTVA;
    private TextBox txtTotalTVA;
    private Label lblTotalTTC;
    private TextBox txtTotalTTC;
    
    // Conditions de paiement
    private GroupBox gbConditionsPaiement;
    private Label lblDelaiPaiement;
    private NumericUpDown nudDelaiPaiement;
    private Label lblModePaiement;
    private ComboBox cmbModePaiement;
    private Label lblCommentaires;
    private TextBox txtCommentaires;
    
    // Statut et suivi
    private GroupBox gbStatutSuivi;
    private Label lblStatut;
    private ComboBox cmbStatut;
    private Label lblSoldeRestant;
    private TextBox txtSoldeRestant;
    private Button btnVoirPaiements;
    private Button btnCreerRelance;
    
    // Boutons d'action
    private Panel pnlBoutons;
    private Button btnEnregistrer;
    private Button btnAnnuler;
    private Button btnImprimer;
    private Button btnEnvoyer;
    private Button btnDupliquer;
}
```

### 5. FORMULAIRE PAIEMENT (PaiementForm)

#### 5.1 Spécifications
- **Type** : Formulaire modal
- **Taille** : 600x500 pixels
- **Calculs** : Automatiques en temps réel

#### 5.2 Structure du PaiementForm.Designer.cs
```csharp
partial class PaiementForm
{
    private System.ComponentModel.IContainer components = null;
    
    // Informations facture
    private GroupBox gbFacture;
    private Label lblFacture;
    private ComboBox cmbFacture;
    private Label lblMontantFacture;
    private TextBox txtMontantFacture;
    private Label lblSoldeAvantPaiement;
    private TextBox txtSoldeAvantPaiement;
    
    // Détails du paiement
    private GroupBox gbPaiement;
    private Label lblMontantPaiement;
    private NumericUpDown nudMontantPaiement;
    private Label lblDatePaiement;
    private DateTimePicker dtpDatePaiement;
    private Label lblTypePaiement;
    private ComboBox cmbTypePaiement;
    
    // Informations spécifiques selon le type
    private GroupBox gbInformationsSpecifiques;
    private Label lblNumeroCheque;
    private TextBox txtNumeroCheque;
    private Label lblBanque;
    private TextBox txtBanque;
    private Label lblNumeroVirement;
    private TextBox txtNumeroVirement;
    
    // Rapprochement
    private GroupBox gbRapprochement;
    private CheckBox chkRapproche;
    private Label lblDateRapprochement;
    private DateTimePicker dtpDateRapprochement;
    private Label lblCommentaire;
    private TextBox txtCommentaire;
    
    // Résultat
    private GroupBox gbResultat;
    private Label lblSoldeApres;
    private TextBox txtSoldeApres;
    private Label lblStatutFacture;
    private TextBox txtStatutFacture;
    
    // Boutons d'action
    private Panel pnlBoutons;
    private Button btnEnregistrer;
    private Button btnAnnuler;
    private Button btnImprimer;
}
```

### 6. FORMULAIRE RELANCES (RelanceForm)

#### 6.1 Spécifications
- **Type** : Formulaire modal
- **Taille** : 1000x700 pixels
- **Fonctionnalités** : Aperçu en temps réel, templates

#### 6.2 Structure du RelanceForm.Designer.cs
```csharp
partial class RelanceForm
{
    private System.ComponentModel.IContainer components = null;
    
    // Sélection des factures
    private GroupBox gbSelectionFactures;
    private DataGridView dgvFacturesRelance;
    private DataGridViewCheckBoxColumn colSelectionner;
    private DataGridViewTextBoxColumn colNumeroFacture;
    private DataGridViewTextBoxColumn colClient;
    private DataGridViewTextBoxColumn colMontant;
    private DataGridViewTextBoxColumn colJoursRetard;
    private DataGridViewTextBoxColumn colDerniereRelance;
    
    // Paramètres de relance
    private GroupBox gbParametresRelance;
    private Label lblTypeRelance;
    private ComboBox cmbTypeRelance;
    private Label lblNiveauRelance;
    private ComboBox cmbNiveauRelance;
    private Label lblTemplate;
    private ComboBox cmbTemplate;
    private Button btnGererTemplates;
    
    // Contenu de la relance
    private GroupBox gbContenuRelance;
    private Label lblObjet;
    private TextBox txtObjet;
    private Label lblCorps;
    private RichTextBox rtbCorpsMessage;
    
    // Aperçu
    private GroupBox gbApercu;
    private WebBrowser wbApercu;
    private Button btnActualiserApercu;
    
    // Options d'envoi
    private GroupBox gbOptionsEnvoi;
    private CheckBox chkEnvoyerEmail;
    private CheckBox chkImprimerCourrier;
    private CheckBox chkEnvoyerSMS;
    private Label lblDateEnvoi;
    private DateTimePicker dtpDateEnvoi;
    
    // Boutons d'action
    private Panel pnlBoutons;
    private Button btnEnvoyer;
    private Button btnProgrammer;
    private Button btnApercu;
    private Button btnAnnuler;
}
```

### 7. CONTRÔLES UTILISATEUR PERSONNALISÉS

#### 7.1 DashboardControl.Designer.cs
```csharp
partial class DashboardControl
{
    private System.ComponentModel.IContainer components = null;
    
    // Indicateurs principaux
    private Panel pnlIndicateurs;
    private Label lblTotalCreances;
    private Label lblValeurTotalCreances;
    private Label lblCreancesEchues;
    private Label lblValeurCreancesEchues;
    private Label lblTauxRecouvrement;
    private Label lblValeurTauxRecouvrement;
    
    // Graphiques
    private Panel pnlGraphiques;
    private Chart chartEvolutionCreances;
    private Chart chartRepartitionClients;
    private Chart chartPerformanceRecouvrement;
    
    // Alertes
    private Panel pnlAlertes;
    private ListView lvAlertes;
    private ColumnHeader chTypeAlerte;
    private ColumnHeader chMessage;
    private ColumnHeader chDate;
    private ColumnHeader chPriorite;
    
    // Actions rapides
    private Panel pnlActionsRapides;
    private Button btnNouvelleFacture;
    private Button btnNouveauPaiement;
    private Button btnRelancesAutomatiques;
    private Button btnRapportJournalier;
}
```

### 8. GESTION DES ÉVÉNEMENTS ET VALIDATION

#### 8.1 Validation en Temps Réel
```csharp
// Dans le code-behind des formulaires
private void txtEmail_Validating(object sender, CancelEventArgs e)
{
    TextBox textBox = sender as TextBox;
    if (!IsValidEmail(textBox.Text))
    {
        errorProvider1.SetError(textBox, "Format d'email invalide");
        e.Cancel = true;
    }
    else
    {
        errorProvider1.SetError(textBox, "");
    }
}

private void nudMontant_ValueChanged(object sender, EventArgs e)
{
    CalculerTotaux();
    UpdateUI();
}
```

### 9. CONFIGURATION DES GRILLES

#### 9.1 Configuration Standard des DataGridView
```csharp
private void ConfigurerDataGridView(DataGridView dgv)
{
    dgv.AutoGenerateColumns = false;
    dgv.AllowUserToAddRows = false;
    dgv.AllowUserToDeleteRows = false;
    dgv.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
    dgv.MultiSelect = false;
    dgv.ReadOnly = true;
    dgv.RowHeadersVisible = false;
    dgv.EnableHeadersVisualStyles = false;
    dgv.AlternatingRowsDefaultCellStyle.BackColor = Color.LightGray;
    
    // Pas de pagination - chargement intelligent
    dgv.VirtualMode = true;
    dgv.CellValueNeeded += DgvFactures_CellValueNeeded;
}
```

### 10. THÈME ET APPARENCE

#### 10.1 Couleurs et Styles Standards
```csharp
public static class ThemeManager
{
    public static readonly Color PrimaryColor = Color.FromArgb(0, 122, 204);
    public static readonly Color SecondaryColor = Color.FromArgb(240, 240, 240);
    public static readonly Color AccentColor = Color.FromArgb(255, 193, 7);
    public static readonly Color ErrorColor = Color.FromArgb(220, 53, 69);
    public static readonly Color SuccessColor = Color.FromArgb(40, 167, 69);
    
    public static void ApplyTheme(Form form)
    {
        form.BackColor = SecondaryColor;
        ApplyThemeToControls(form.Controls);
    }
}
```

### 11. GESTION DES RESSOURCES

#### 11.1 Fichiers .resx pour l'Internationalisation
```xml
<!-- Strings.resx -->
<data name="ButtonSave" xml:space="preserve">
    <value>Enregistrer</value>
</data>
<data name="ButtonCancel" xml:space="preserve">
    <value>Annuler</value>
</data>
<data name="ValidationEmailRequired" xml:space="preserve">
    <value>L'adresse email est obligatoire</value>
</data>
```

---

**Ces spécifications détaillées garantissent que tous les formulaires Windows Forms seront créés avec leurs fichiers Designer appropriés, permettant une utilisation optimale du concepteur Visual Studio et une maintenance facilitée du code.**
