# SPÉCIFICATIONS DE PERFORMANCE ET OPTIMISATION
## Application de Recouvrement de Créances - 100+ Factures/Jour

### 1. OBJECTIFS DE PERFORMANCE

#### 1.1 Métriques Cibles
- **Volume** : Support de 100+ factures par jour (36 500+ factures/an)
- **Utilisateurs simultanés** : 10-20 utilisateurs
- **Temps de réponse** : < 2 secondes pour les opérations courantes
- **Disponibilité** : 99.5% (8h-18h, lundi-vendredi)
- **Capacité de stockage** : 5 ans d'historique minimum

#### 1.2 Contraintes Techniques
- **Pas de pagination** dans les grilles (chargement intelligent)
- **Chargement asynchrone** obligatoire
- **Cache intelligent** pour les données fréquemment consultées
- **Optimisation SQL** avec Dapper

### 2. ARCHITECTURE DE PERFORMANCE

#### 2.1 Stratégie de Chargement des Données
```csharp
public class DataLoadingStrategy
{
    // Chargement virtuel pour les grandes listes
    public async Task<VirtualDataSource<T>> CreateVirtualDataSourceAsync<T>(
        Func<int, int, Task<IEnumerable<T>>> dataLoader,
        Func<Task<int>> countLoader)
    {
        return new VirtualDataSource<T>
        {
            DataLoader = dataLoader,
            CountLoader = countLoader,
            PageSize = 100, // Chargement par blocs
            CacheSize = 1000 // Cache des 1000 derniers éléments
        };
    }
    
    // Chargement progressif sans pagination visible
    public async Task LoadDataProgressivelyAsync<T>(
        DataGridView grid,
        IAsyncEnumerable<T> dataStream)
    {
        await foreach (var batch in dataStream.Buffer(50))
        {
            grid.Invoke(() => {
                foreach (var item in batch)
                {
                    grid.Rows.Add(CreateRow(item));
                }
            });
            
            // Pause pour maintenir la réactivité de l'UI
            await Task.Delay(10);
        }
    }
}
```

#### 2.2 Gestion du Cache Multi-Niveau
```csharp
public class CacheManager
{
    private readonly MemoryCache _memoryCache;
    private readonly IDistributedCache _distributedCache;
    
    // Cache L1 : Mémoire locale (données fréquentes)
    public async Task<T> GetFromL1CacheAsync<T>(string key)
    {
        return _memoryCache.Get<T>(key);
    }
    
    // Cache L2 : Cache distribué (données partagées)
    public async Task<T> GetFromL2CacheAsync<T>(string key)
    {
        var data = await _distributedCache.GetStringAsync(key);
        return data != null ? JsonConvert.DeserializeObject<T>(data) : default(T);
    }
    
    // Stratégie de cache intelligent
    public async Task<T> GetWithCacheStrategyAsync<T>(
        string key, 
        Func<Task<T>> dataLoader,
        TimeSpan? expiration = null)
    {
        // Tentative L1
        var result = await GetFromL1CacheAsync<T>(key);
        if (result != null) return result;
        
        // Tentative L2
        result = await GetFromL2CacheAsync<T>(key);
        if (result != null)
        {
            // Promotion vers L1
            _memoryCache.Set(key, result, TimeSpan.FromMinutes(15));
            return result;
        }
        
        // Chargement depuis la source
        result = await dataLoader();
        
        // Mise en cache
        await SetCacheAsync(key, result, expiration ?? TimeSpan.FromHours(1));
        
        return result;
    }
}
```

### 3. OPTIMISATIONS BASE DE DONNÉES

#### 3.1 Index Optimisés pour le Volume
```sql
-- Index pour les requêtes fréquentes sur les factures
CREATE NONCLUSTERED INDEX IX_Factures_Performance 
ON Factures (ClientId, DateEcheance, Statut)
INCLUDE (NumeroFacture, MontantTTC);

-- Index pour le calcul des soldes
CREATE NONCLUSTERED INDEX IX_Paiements_Calcul_Solde 
ON Paiements (FactureId, DatePaiement)
INCLUDE (MontantPaiement);

-- Index pour les relances automatiques
CREATE NONCLUSTERED INDEX IX_Factures_Relances 
ON Factures (DateEcheance, Statut, ClientId)
WHERE Statut IN (0, 1); -- Seulement les factures en cours

-- Index pour les rapports de performance
CREATE NONCLUSTERED INDEX IX_Factures_Reporting 
ON Factures (DateFacture, Statut)
INCLUDE (ClientId, MontantTTC);
```

#### 3.2 Requêtes Optimisées avec Dapper
```csharp
public class OptimizedFactureRepository : IFactureRepository
{
    // Requête optimisée pour le dashboard (pas de pagination)
    public async Task<IEnumerable<FactureDashboardDto>> GetFacturesForDashboardAsync()
    {
        const string sql = @"
            WITH FacturesAvecSolde AS (
                SELECT 
                    f.FactureId,
                    f.NumeroFacture,
                    f.ClientId,
                    c.RaisonSociale,
                    f.DateFacture,
                    f.DateEcheance,
                    f.MontantTTC,
                    f.Statut,
                    ISNULL(SUM(p.MontantPaiement), 0) as TotalPaiements,
                    f.MontantTTC - ISNULL(SUM(p.MontantPaiement), 0) as Solde,
                    DATEDIFF(DAY, f.DateEcheance, GETDATE()) as JoursRetard
                FROM Factures f
                INNER JOIN Clients c ON f.ClientId = c.ClientId
                LEFT JOIN Paiements p ON f.FactureId = p.FactureId
                WHERE f.Statut IN (0, 1) -- En cours ou partiellement payée
                GROUP BY f.FactureId, f.NumeroFacture, f.ClientId, c.RaisonSociale,
                         f.DateFacture, f.DateEcheance, f.MontantTTC, f.Statut
            )
            SELECT *
            FROM FacturesAvecSolde
            WHERE Solde > 0
            ORDER BY 
                CASE WHEN JoursRetard > 0 THEN 0 ELSE 1 END, -- Échues en premier
                JoursRetard DESC,
                DateEcheance ASC";
                
        return await Connection.QueryAsync<FactureDashboardDto>(sql);
    }
    
    // Chargement par blocs pour éviter la pagination
    public async Task<IEnumerable<T>> LoadDataInBatchesAsync<T>(
        string baseQuery, 
        object parameters,
        int batchSize = 1000)
    {
        var offset = 0;
        var results = new List<T>();
        
        while (true)
        {
            var batchQuery = $@"
                {baseQuery}
                ORDER BY FactureId
                OFFSET {offset} ROWS
                FETCH NEXT {batchSize} ROWS ONLY";
                
            var batch = await Connection.QueryAsync<T>(batchQuery, parameters);
            
            if (!batch.Any()) break;
            
            results.AddRange(batch);
            offset += batchSize;
            
            // Limite de sécurité pour éviter les requêtes infinies
            if (offset > 100000) break;
        }
        
        return results;
    }
}
```

### 4. OPTIMISATIONS INTERFACE UTILISATEUR

#### 4.1 DataGridView Virtuel Sans Pagination
```csharp
public class VirtualDataGridView : DataGridView
{
    private VirtualDataSource _dataSource;
    private readonly Dictionary<int, object> _cache = new Dictionary<int, object>();
    
    public void SetVirtualDataSource(VirtualDataSource dataSource)
    {
        _dataSource = dataSource;
        this.VirtualMode = true;
        this.RowCount = dataSource.TotalCount;
    }
    
    protected override void OnCellValueNeeded(DataGridViewCellValueEventArgs e)
    {
        // Cache intelligent pour éviter les requêtes répétées
        if (!_cache.ContainsKey(e.RowIndex))
        {
            // Chargement par blocs
            var startIndex = (e.RowIndex / 100) * 100;
            var endIndex = Math.Min(startIndex + 100, _dataSource.TotalCount);
            
            var data = _dataSource.LoadRange(startIndex, endIndex - startIndex);
            
            for (int i = 0; i < data.Count; i++)
            {
                _cache[startIndex + i] = data[i];
            }
        }
        
        var item = _cache[e.RowIndex];
        e.Value = GetPropertyValue(item, this.Columns[e.ColumnIndex].DataPropertyName);
        
        base.OnCellValueNeeded(e);
    }
    
    // Nettoyage du cache pour éviter la consommation excessive de mémoire
    private void CleanupCache()
    {
        if (_cache.Count > 5000) // Limite du cache
        {
            var keysToRemove = _cache.Keys.OrderBy(k => k).Take(1000).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
            }
        }
    }
}
```

#### 4.2 Chargement Asynchrone des Formulaires
```csharp
public partial class MainForm : Form
{
    private readonly SemaphoreSlim _loadingSemaphore = new SemaphoreSlim(1, 1);
    private CancellationTokenSource _cancellationTokenSource;
    
    public async Task LoadDataAsync()
    {
        await _loadingSemaphore.WaitAsync();
        
        try
        {
            _cancellationTokenSource = new CancellationTokenSource();
            var token = _cancellationTokenSource.Token;
            
            // Affichage de l'indicateur de chargement
            ShowLoadingIndicator(true);
            
            // Chargement parallèle des données
            var tasks = new[]
            {
                LoadFacturesAsync(token),
                LoadClientsAsync(token),
                LoadDashboardAsync(token),
                LoadAlertsAsync(token)
            };
            
            await Task.WhenAll(tasks);
            
            // Mise à jour de l'interface
            UpdateUI();
        }
        catch (OperationCanceledException)
        {
            // Chargement annulé
        }
        finally
        {
            ShowLoadingIndicator(false);
            _loadingSemaphore.Release();
        }
    }
    
    private async Task LoadFacturesAsync(CancellationToken token)
    {
        var factures = await _factureService.GetFacturesAsync(token);
        
        // Mise à jour thread-safe de l'interface
        this.Invoke(() =>
        {
            dgvFactures.DataSource = factures;
            UpdateFacturesCount(factures.Count());
        });
    }
}
```

### 5. GESTION MÉMOIRE ET RESSOURCES

#### 5.1 Pool d'Objets pour les Entités Fréquentes
```csharp
public class EntityPool<T> where T : class, new()
{
    private readonly ConcurrentQueue<T> _pool = new ConcurrentQueue<T>();
    private readonly Func<T> _factory;
    private readonly Action<T> _resetAction;
    
    public EntityPool(Func<T> factory = null, Action<T> resetAction = null)
    {
        _factory = factory ?? (() => new T());
        _resetAction = resetAction ?? (obj => { });
    }
    
    public T Rent()
    {
        if (_pool.TryDequeue(out T item))
        {
            return item;
        }
        
        return _factory();
    }
    
    public void Return(T item)
    {
        if (item != null)
        {
            _resetAction(item);
            _pool.Enqueue(item);
        }
    }
}

// Utilisation
private readonly EntityPool<FactureDto> _facturePool = 
    new EntityPool<FactureDto>(resetAction: f => f.Reset());
```

#### 5.2 Monitoring des Performances
```csharp
public class PerformanceMonitor
{
    private readonly ILogger _logger;
    private readonly Dictionary<string, PerformanceCounter> _counters;
    
    public async Task<T> MeasureAsync<T>(string operationName, Func<Task<T>> operation)
    {
        var stopwatch = Stopwatch.StartNew();
        var memoryBefore = GC.GetTotalMemory(false);
        
        try
        {
            var result = await operation();
            
            stopwatch.Stop();
            var memoryAfter = GC.GetTotalMemory(false);
            
            LogPerformance(operationName, stopwatch.ElapsedMilliseconds, 
                          memoryAfter - memoryBefore);
            
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'opération {OperationName}", operationName);
            throw;
        }
    }
    
    private void LogPerformance(string operation, long elapsedMs, long memoryUsed)
    {
        if (elapsedMs > 2000) // Alerte si > 2 secondes
        {
            _logger.LogWarning("Performance dégradée: {Operation} - {ElapsedMs}ms - {MemoryUsed}bytes",
                             operation, elapsedMs, memoryUsed);
        }
    }
}
```

### 6. OPTIMISATIONS SPÉCIFIQUES AU MÉTIER

#### 6.1 Calcul Optimisé des Soldes
```csharp
public class SoldeCalculationService
{
    private readonly IMemoryCache _cache;
    
    // Cache des soldes avec invalidation intelligente
    public async Task<decimal> CalculerSoldeFactureAsync(int factureId)
    {
        var cacheKey = $"solde_facture_{factureId}";
        
        if (_cache.TryGetValue(cacheKey, out decimal soldeCache))
        {
            return soldeCache;
        }
        
        var solde = await CalculerSoldeFromDatabaseAsync(factureId);
        
        // Cache avec expiration courte pour les données financières
        _cache.Set(cacheKey, solde, TimeSpan.FromMinutes(5));
        
        return solde;
    }
    
    // Calcul en lot pour optimiser les performances
    public async Task<Dictionary<int, decimal>> CalculerSoldesEnLotAsync(IEnumerable<int> factureIds)
    {
        const string sql = @"
            SELECT 
                f.FactureId,
                f.MontantTTC - ISNULL(SUM(p.MontantPaiement), 0) as Solde
            FROM Factures f
            LEFT JOIN Paiements p ON f.FactureId = p.FactureId
            WHERE f.FactureId IN @FactureIds
            GROUP BY f.FactureId, f.MontantTTC";
            
        var results = await Connection.QueryAsync<(int FactureId, decimal Solde)>(
            sql, new { FactureIds = factureIds });
            
        return results.ToDictionary(r => r.FactureId, r => r.Solde);
    }
}
```

#### 6.2 Système de Relances Optimisé
```csharp
public class RelanceOptimizedService
{
    // Traitement par lots pour les relances automatiques
    public async Task ExecuterRelancesAutomatiquesAsync()
    {
        const int batchSize = 100;
        var offset = 0;
        
        while (true)
        {
            var factures = await GetFacturesEchuesPourRelanceAsync(offset, batchSize);
            
            if (!factures.Any()) break;
            
            // Traitement parallèle par petits groupes
            var tasks = factures
                .GroupBy(f => f.ClientId)
                .Select(group => TraiterRelancesClientAsync(group.Key, group.ToList()));
                
            await Task.WhenAll(tasks);
            
            offset += batchSize;
        }
    }
    
    private async Task TraiterRelancesClientAsync(int clientId, List<FactureDto> factures)
    {
        // Regroupement intelligent des relances par client
        var relanceGroupee = new RelanceGroupeeDto
        {
            ClientId = clientId,
            Factures = factures,
            TypeRelance = DeterminerTypeRelance(factures),
            DateEnvoi = DateTime.Now
        };
        
        await EnvoyerRelanceGroupeeAsync(relanceGroupee);
    }
}
```

### 7. CONFIGURATION DE PRODUCTION

#### 7.1 Paramètres de Performance
```xml
<!-- App.config optimisé pour la production -->
<configuration>
  <appSettings>
    <!-- Cache -->
    <add key="CacheExpirationMinutes" value="30" />
    <add key="MaxCacheSize" value="1000" />
    
    <!-- Base de données -->
    <add key="ConnectionPoolSize" value="20" />
    <add key="CommandTimeout" value="30" />
    <add key="BatchSize" value="100" />
    
    <!-- Interface -->
    <add key="VirtualGridPageSize" value="100" />
    <add key="MaxRowsWithoutVirtualization" value="1000" />
    
    <!-- Performance -->
    <add key="EnablePerformanceLogging" value="true" />
    <add key="PerformanceThresholdMs" value="2000" />
  </appSettings>
  
  <connectionStrings>
    <add name="DefaultConnection" 
         connectionString="Server=.\SQLEXPRESS;Database=RecouvrementCreances;Integrated Security=true;TrustServerCertificate=true;Connection Timeout=30;Command Timeout=30;Pooling=true;Max Pool Size=20;" />
  </connectionStrings>
</configuration>
```

### 8. TESTS DE CHARGE

#### 8.1 Scénarios de Test
```csharp
[TestClass]
public class PerformanceTests
{
    [TestMethod]
    public async Task Test_Chargement_1000_Factures_Moins_2_Secondes()
    {
        // Arrange
        var stopwatch = Stopwatch.StartNew();
        
        // Act
        var factures = await _factureService.GetFacturesAsync();
        stopwatch.Stop();
        
        // Assert
        Assert.IsTrue(factures.Count() >= 1000);
        Assert.IsTrue(stopwatch.ElapsedMilliseconds < 2000);
    }
    
    [TestMethod]
    public async Task Test_Utilisateurs_Simultanees()
    {
        // Simulation de 20 utilisateurs simultanés
        var tasks = Enumerable.Range(1, 20)
            .Select(i => SimulerUtilisateurAsync(i))
            .ToArray();
            
        var results = await Task.WhenAll(tasks);
        
        Assert.IsTrue(results.All(r => r.Success));
        Assert.IsTrue(results.Average(r => r.ResponseTimeMs) < 3000);
    }
}
```

---

**Ces spécifications de performance garantissent que l'application pourra gérer efficacement plus de 100 factures par jour avec une interface réactive sans pagination, tout en maintenant des temps de réponse optimaux pour tous les utilisateurs.**
